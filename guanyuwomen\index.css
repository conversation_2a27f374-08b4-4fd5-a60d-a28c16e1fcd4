/* 基础样式重置和全局设置 */
* {
	margin: 0;
	padding: 0;
	box-sizing: border-box;
}

body {
	font-family: "AlibabaPuHuiTi-Regular", "PingFang SC", "Microsoft YaHei",
		sans-serif;
	line-height: 1.6;
	color: #333;
	background-color: #fff;
}

.page {
	width: 100%;
	min-height: 100vh;
}

.container {
	max-width: 1030px;
	margin: 0 auto;
	padding: 0;
}

/* 顶部导航样式 */
.header {
	background-color: #000;
	position: relative;
}

.header-container {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20px 0;
	max-width: 1200px;
	margin: 0 auto;
	padding-left: 20px;
	padding-right: 20px;
}

.logo-section {
	display: flex;
	align-items: center;
	gap: 12px;
}

.logo-icon {
	width: 42px;
	height: 42px;
	border-radius: 50%;
	background-image: url(./img/6002fce2fbf946d3849ae479605d68ed_mergeImage.png);
	background-size: cover;
	background-position: center;
	border: 1px solid #979797;
}

.company-name {
	color: #fff;
	font-size: 30px;
	font-weight: 500;
	margin: 0;
}

.main-nav {
	display: flex;
	align-items: center;
	gap: 32px;
}

.nav-link {
	color: #fff;
	text-decoration: none;
	font-size: 18px;
	transition: color 0.3s ease;
}

.nav-link:hover {
	color: #0066cc;
}

.contact-btn {
	background-color: #fff;
	color: #000;
	padding: 12px 24px;
	border: none;
	font-size: 18px;
	cursor: pointer;
	transition: all 0.3s ease;
}

.contact-btn:hover {
	background-color: #f0f0f0;
	transform: translateY(-2px);
}

/* 二级导航样式 */
.secondary-nav {
	background-color: #fff;
	padding: 20px 0;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 20px;
	position: relative;
}

.nav-indicator {
	color: #ff1d1d;
	font-size: 14px;
}

.nav-divider {
	width: 62px;
	height: 1px;
	background-color: #ccc;
}

.nav-menu {
	display: flex;
	align-items: center;
	gap: 20px;
}

.nav-item {
	color: #000;
	text-decoration: none;
	font-size: 16px;
	padding: 8px 16px;
	transition: all 0.3s ease;
}

.nav-item.active {
	background-color: #0e6be4;
	color: #fff;
}

.nav-item:hover:not(.active) {
	background-color: #f0f0f0;
}

.nav-arrow {
	width: 14px;
	height: 14px;
	background-image: url(./img/SketchPng877a2df19c62106064ae9368d65300ad01e4c332b3c372f32108e55019e19d0c.png);
	background-size: contain;
	background-repeat: no-repeat;
}

/* 英雄区域样式 */
.hero-section {
	background: url(./img/SketchPngc22cd3bfbdf2b4c82b16fe88718024391d5b03af75c1b89a6da888553145132a.png)
		center/cover no-repeat;
	height: 520px;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	color: #fff;
}

.hero-content {
	text-align: center;
}

.hero-title {
	font-size: 48px;
	font-weight: normal;
	margin-bottom: 10px;
	line-height: 1.5;
}

.hero-subtitle {
	font-size: 24px;
	font-weight: 300;
	margin: 0;
}

/* 主要内容区域样式 */
.main-content {
	background-color: #fff;
}

/* 公司介绍区域 */
.company-intro {
	padding: 60px 0;
	background-color: #fff;
}

.intro-text {
	font-size: 24px;
	font-weight: 300;
	line-height: 1.4;
	text-align: center;
	margin-bottom: 60px;
	max-width: 900px;
	margin-left: auto;
	margin-right: auto;
}

/* 公司宗旨标题区域 */
.purpose-title-section {
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
}

.section-title {
	font-size: 36px;
	font-weight: normal;
	margin: 0;
	color: #333;
	padding-bottom: 22px;
	position: relative;
	z-index: 2;
}

.title-decoration-line {
	position: absolute;
	top: 55%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: auto;
	height: auto;
	max-width: 400px;
	z-index: 1;
}

/* 我们的宗旨内容区域 */
.purpose-content-section {
	background: url(./img/b8d447e677534c7b957e2e9fc11992af_mergeImage.png)
		center/contain no-repeat;
	border-radius: 8px;
	position: relative;
	min-height: 500px;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 -25px;
}

.purpose-text-content {
	padding: 80px 65px 80px 0;
	max-width: 625px;
	margin-left: auto;
}

.purpose-subtitle {
	font-size: 36px;
	font-weight: normal;
	margin-bottom: 20px;
	color: #333;
}

.purpose-description {
	font-size: 16px;
	line-height: 1.6;
	color: #333;
	text-align: justify;
}

/* 业务板块区域 */
.business-section {
	padding: 80px 0;
	background-color: #f8f9fa;
}

.business-header {
	text-align: center;
	margin-bottom: 60px;
}

.section-description {
	font-size: 16px;
	line-height: 1.6;
	color: #666;
	max-width: 600px;
	margin: 0 auto;
}

.business-cards {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
	gap: 24px;
	margin-top: 40px;
}

.business-card {
	background: #fff;
	border-radius: 8px;
	box-shadow: 0 2px 12px rgba(86, 86, 86, 0.1);
	overflow: hidden;
	transition: all 0.3s ease;
}

.business-card:hover {
	transform: translateY(-4px);
	box-shadow: 0 8px 24px rgba(86, 86, 86, 0.15);
}

.card-image {
	height: 200px;
	overflow: hidden;
}

.card-image img {
	width: 100%;
	height: 100%;
	object-fit: cover;
	transition: transform 0.3s ease;
}

.business-card:hover .card-image img {
	transform: scale(1.05);
}

.card-content {
	padding: 24px;
}

.card-title {
	font-size: 18px;
	font-weight: normal;
	margin-bottom: 16px;
	color: #333;
}

.card-description {
	font-size: 14px;
	line-height: 1.6;
	color: #666;
}

/* 视频区域 */
.video-section {
	padding: 80px 0;
	background-color: #fff;
}

.video-header {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 12px;
	margin-bottom: 20px;
}

.video-label {
	font-size: 14px;
	color: #333;
}

.video-arrow {
	width: 7px;
	height: 14px;
	background-image: url(./img/SketchPnge5e249ccce36abb7acdfb3d406d18f27632f85fc6421796ca49dcdde4b914dda.png);
	background-size: contain;
	background-repeat: no-repeat;
}

.video-container {
	max-width: 860px;
	margin: 0 auto;
}

.video-thumbnail {
	position: relative;
	border-radius: 8px;
	overflow: hidden;
	cursor: pointer;
}

.video-thumbnail img {
	width: 100%;
	height: 500px;
	object-fit: cover;
}

.play-button {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	background: rgba(0, 0, 0, 0.6);
	border: none;
	border-radius: 50%;
	width: 100px;
	height: 100px;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.3s ease;
}

.play-button:hover {
	background: rgba(0, 0, 0, 0.8);
	transform: translate(-50%, -50%) scale(1.1);
}

.play-button img {
	width: 50px;
	height: 50px;
}

/* 底部导航区域 */
.bottom-nav-section {
	background: url(./img/SketchPngc665d90b5754568cc23004f00a8304b254b404ddd99665b5d13240330c29f054.png)
		center/cover no-repeat;
	padding: 60px 0;
	color: #fff;
}

.bottom-nav {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
	gap: 40px;
	margin-bottom: 40px;
}

.nav-column {
	display: flex;
	flex-direction: column;
	gap: 12px;
}

.nav-title {
	font-size: 16px;
	font-weight: 700;
	margin-bottom: 8px;
	color: #fff;
}

.bottom-nav .nav-link {
	color: #fff;
	text-decoration: none;
	font-size: 16px;
	transition: color 0.3s ease;
}

.bottom-nav .nav-link:hover {
	color: #0066cc;
}

.company-footer {
	display: flex;
	align-items: center;
	justify-content: center;
	padding-top: 40px;
	border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.company-logo {
	display: flex;
	align-items: center;
	gap: 12px;
}

.company-logo img {
	width: 56px;
	height: 55px;
}

.company-logo .company-name {
	font-size: 30px;
	font-weight: 500;
	color: #000;
}

/* 战略区域 */
.strategy-section {
	padding: 80px 0;
	background: linear-gradient(270deg, #dfdddd 0%, #f4f4f4 100%);
}
.strategy-text {
	padding-top: 36px;
}
.strategy-content {
	display: grid;
	grid-template-columns: 1fr 360px;
	gap: 40px;
	background: #fff;
	border-radius: 8px;
	padding: 8px 40px;
	box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.strategy-title {
	font-size: 36px;
	line-height: 36px;
	font-weight: normal;
	margin-bottom: 20px;
	color: #333;
}

.strategy-description {
	font-size: 16px;
	line-height: 22px;
	color: #333;
	text-align: justify;
}

.strategy-image img {
	width: 100%;
	height: auto;
	border-radius: 8px;
}

/* 多元化与包容性区域 */
.diversity-section {
	padding: 80px 0;
	background: url(./img/89c9827e71bd47e995825799d1464712_mergeImage.png)
		center/cover no-repeat;
}

.diversity-content {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 8px;
	padding: 60px;
	max-width: 900px;
	margin: 0 auto;
}

.diversity-description {
	font-size: 16px;
	line-height: 1.6;
	color: #333;
	text-align: justify;
}

/* 响应式设计 */
@media (max-width: 1200px) {
	.container {
		max-width: 960px;
	}

	.header-container {
		max-width: 960px;
	}

	.hero-title {
		font-size: 40px;
	}

	.hero-subtitle {
		font-size: 20px;
	}
}

@media (max-width: 900px) {
	.container {
		max-width: 720px;
		padding: 0 16px;
	}

	.header-container {
		max-width: 720px;
		padding-left: 16px;
		padding-right: 16px;
	}

	.main-nav {
		gap: 20px;
	}

	.nav-link {
		font-size: 16px;
	}

	.contact-btn {
		padding: 10px 20px;
		font-size: 16px;
	}

	.hero-title {
		font-size: 36px;
	}

	.hero-subtitle {
		font-size: 18px;
	}

	.purpose-content-section {
		min-height: 400px;
		padding: 20px;
	}

	.purpose-text-content {
		padding: 30px 20px;
		max-width: 100%;
	}

	.business-cards {
		grid-template-columns: 1fr;
		gap: 20px;
	}

	.strategy-content {
		grid-template-columns: 1fr;
		gap: 30px;
	}

	.strategy-image {
		order: -1;
	}

	.bottom-nav {
		grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
		gap: 30px;
	}
}

@media (max-width: 600px) {
	.container {
		padding: 0 12px;
	}

	.header-container {
		flex-direction: column;
		gap: 20px;
		padding: 16px 12px;
	}

	.logo-section {
		justify-content: center;
	}

	.main-nav {
		flex-wrap: wrap;
		justify-content: center;
		gap: 16px;
	}

	.secondary-nav {
		flex-direction: column;
		gap: 12px;
		padding: 16px 0;
	}

	.nav-menu {
		flex-wrap: wrap;
		justify-content: center;
		gap: 12px;
	}

	.hero-section {
		height: 400px;
		padding: 0 12px;
	}

	.hero-title {
		font-size: 28px;
	}

	.hero-subtitle {
		font-size: 16px;
	}

	.section-title {
		font-size: 28px;
	}

	.intro-text {
		font-size: 18px;
	}

	.title-decoration-line {
		max-width: 280px;
	}

	.section-title {
		font-size: 28px;
		padding: 0 15px;
	}

	.purpose-content-section {
		min-height: 350px;
		padding: 15px;
	}

	.purpose-text-content {
		padding: 25px 15px;
		max-width: 100%;
	}

	.purpose-subtitle {
		font-size: 24px;
	}

	.company-intro,
	.business-section,
	.video-section,
	.strategy-section,
	.diversity-section,
	.bottom-nav-section {
		padding: 40px 0;
	}

	.diversity-content {
		padding: 30px 20px;
	}

	.strategy-content {
		padding: 30px 20px;
	}

	.strategy-title {
		font-size: 24px;
	}

	.video-thumbnail img {
		height: 300px;
	}

	.play-button {
		width: 80px;
		height: 80px;
	}

	.play-button img {
		width: 40px;
		height: 40px;
	}

	.bottom-nav {
		grid-template-columns: 1fr;
		gap: 20px;
		text-align: center;
	}

	.company-footer {
		flex-direction: column;
		gap: 16px;
	}
}

/* 额外的交互效果和动画 */
.nav-link,
.nav-item,
.contact-btn,
.business-card,
.play-button {
	transition: all 0.3s ease;
}

.business-card:hover {
	transform: translateY(-4px);
}

.contact-btn:hover {
	transform: translateY(-2px);
}

.play-button:hover {
	transform: translate(-50%, -50%) scale(1.1);
}

/* 确保图片响应式 */
img {
	max-width: 100%;
	height: auto;
}

/* 文本选择样式 */
::selection {
	background-color: #0066cc;
	color: #fff;
}

/* 滚动条样式 */
::-webkit-scrollbar {
	width: 8px;
}

::-webkit-scrollbar-track {
	background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
	background: #888;
	border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
	background: #555;
}
